'use client'

import React, { useState, useRef, useEffect } from 'react'
import ReactMarkdown from 'react-markdown'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import CollapsibleCard from '@/components/ui/CollapsibleCard'
import ModernLoader from '@/components/ui/ModernLoader'
import { useAppStore, useActiveTab, useActiveChatMessages } from '@/lib/store'
import { Send, Brain, MessageCircle, Sparkles } from 'lucide-react'

const AIAssistant: React.FC = () => {
  const { addChatMessage, setProcessing } = useAppStore()
  const activeTab = useActiveTab()
  const chatMessages = useActiveChatMessages()
  const [chatInput, setChatInput] = useState('')
  const chatEndRef = useRef<HTMLDivElement>(null)

  // 自动滚动到聊天底部
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [chatMessages])
  // 处理聊天提交
  const handleChatSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!chatInput.trim() || !activeTab) return

    const userMessage = chatInput.trim()
    setChatInput('')

    try {
      // 添加用户消息
      addChatMessage(activeTab.id, {
        role: 'user',
        content: userMessage
      })

      setProcessing(true)

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          context: {
            originalContent: activeTab.originalContent,
            aiNote: activeTab.aiNoteMarkdown
          }
        })
      })

      if (!response.ok) {
        throw new Error(`聊天请求失败: ${response.status}`)
      }

      const data = await response.json()

      // 验证响应数据
      if (!data.response || typeof data.response !== 'string') {
        throw new Error('无效的AI响应')
      }

      // 添加AI回复
      addChatMessage(activeTab.id, {
        role: 'assistant',
        content: data.response
      })
    } catch (error) {
      console.error('聊天错误:', error)
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      addChatMessage(activeTab.id, {
        role: 'assistant',
        content: `抱歉，我遇到了一些问题：${errorMessage}。请稍后再试。`
      })
    } finally {
      setProcessing(false)
    }
  }



  if (!activeTab) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500 p-6">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
            <Brain className="w-8 h-8 text-blue-600" />
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900 mb-2">AI助手</p>
            <p className="text-sm text-gray-600">选择或创建一个标签页开始使用</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-gray-50/30">
      {/* AI笔记卡片 */}
      <div className="flex-shrink-0 p-4">
        <CollapsibleCard
          title="AI智能笔记"
          icon={<Sparkles className="w-4 h-4" />}
          badge={activeTab.aiNoteMarkdown ? "已生成" : (activeTab.aiAnalyzing || activeTab.isLoading) ? "分析中" : "待分析"}
          variant="gradient"
          className="shadow-sm hover:shadow-md transition-shadow duration-200"
          contentClassName="p-4"
        >
          {activeTab.isLoading || activeTab.aiAnalyzing || (!activeTab.aiNoteMarkdown && activeTab.sourceType === 'url') ? (
            <div className="py-8">
              <ModernLoader
                variant="dots"
                size="md"
                text={
                  activeTab.isLoading
                    ? "AI正在分析..."
                    : activeTab.aiAnalyzing
                      ? "AI正在后台分析网页内容..."
                      : "等待AI分析..."
                }
                className="text-center"
              />
              {(activeTab.aiAnalyzing || activeTab.sourceType === 'url') && !activeTab.isLoading && (
                <p className="text-xs text-gray-500 mt-3 text-center">
                  您可以先浏览网页，分析完成后会自动显示
                </p>
              )}
            </div>
          ) : activeTab.aiNoteMarkdown ? (
            <div className="prose prose-sm max-w-none">
              <SafeMarkdown className="prose prose-sm max-w-none">
                {activeTab.aiNoteMarkdown}
              </SafeMarkdown>
            </div>
          ) : (
            <div className="py-8 text-center">
              <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-3">
                <Brain className="w-6 h-6 text-gray-400" />
              </div>
              <p className="text-gray-500 text-sm">暂无AI笔记</p>
              <p className="text-gray-400 text-xs mt-1">AI将为您的内容生成智能摘要</p>
            </div>
          )}
        </CollapsibleCard>
      </div>

      {/* 聊天区域 - 占据剩余空间 */}
      <div className="flex-1 flex flex-col min-h-0 p-4 pt-0">
        <CollapsibleCard
          title="AI对话"
          icon={<MessageCircle className="w-4 h-4" />}
          badge={chatMessages.length}
          variant="default"
          className="flex-1 flex flex-col min-h-0 shadow-sm"
          contentClassName="flex-1 flex flex-col min-h-0"
        >
          {/* 聊天消息 - 可滚动 */}
          <div className="flex-1 overflow-auto p-4 space-y-4 min-h-0 scrollbar-thin">
            {chatMessages.length === 0 ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 mx-auto bg-blue-50 rounded-full flex items-center justify-center">
                    <MessageCircle className="w-6 h-6 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-gray-600 font-medium">开始对话</p>
                    <p className="text-gray-500 text-sm">与AI深入探讨内容，获得更多见解</p>
                  </div>
                </div>
              </div>
            ) : (
              <>
                {chatMessages.map((message) => {
                  // 确保消息有有效的ID和内容
                  if (!message.id || !message.content) {
                    console.warn('Invalid message:', message)
                    return null
                  }

                  return (
                    <div
                      key={message.id}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[80%] px-4 py-3 rounded-2xl text-sm shadow-sm ${
                          message.role === 'user'
                            ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                            : 'bg-white border border-gray-200 text-gray-900'
                        }`}
                      >
                        {message.role === 'assistant' ? (
                          <SafeMarkdown className="prose prose-sm max-w-none">
                            {message.content}
                          </SafeMarkdown>
                        ) : (
                          <span>{message.content}</span>
                        )}
                      </div>
                    </div>
                  )
                }).filter(Boolean)}
                <div ref={chatEndRef} />
              </>
            )}
          </div>

          {/* 聊天输入 - 固定在底部 */}
          <div className="flex-shrink-0 p-4 border-t border-gray-100 bg-gray-50/50">
            <form onSubmit={handleChatSubmit} className="flex space-x-3">
              <input
                type="text"
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                placeholder="问问AI..."
                className="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 outline-none text-sm bg-white transition-colors"
              />
              <button
                type="submit"
                disabled={!chatInput.trim()}
                className="px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <Send size={16} />
              </button>
            </form>
          </div>
        </CollapsibleCard>
      </div>
    </div>
  )
}

export default AIAssistant
