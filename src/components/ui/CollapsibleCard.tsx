'use client'

import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CollapsibleCardProps {
  title: string
  children: React.ReactNode
  defaultExpanded?: boolean
  className?: string
  headerClassName?: string
  contentClassName?: string
  icon?: React.ReactNode
  badge?: string | number
  variant?: 'default' | 'glass' | 'gradient'
}

const CollapsibleCard: React.FC<CollapsibleCardProps> = ({
  title,
  children,
  defaultExpanded = true,
  className,
  headerClassName,
  contentClassName,
  icon,
  badge,
  variant = 'default'
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)

  const variantClasses = {
    default: 'bg-white border border-gray-200 shadow-sm',
    glass: 'glass-effect border border-white/20',
    gradient: 'bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200/50'
  }

  return (
    <div className={cn(
      'rounded-xl overflow-hidden transition-all duration-300 hover:shadow-md',
      variantClasses[variant],
      className
    )}>
      {/* Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={cn(
          'w-full px-4 py-3 flex items-center justify-between text-left transition-colors',
          'hover:bg-gray-50/50 focus:outline-none focus:ring-2 focus:ring-blue-500/20',
          headerClassName
        )}
      >
        <div className="flex items-center space-x-3">
          {icon && (
            <div className="flex-shrink-0 text-gray-600">
              {icon}
            </div>
          )}
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold text-gray-900 text-sm">
              {title}
            </h3>
            {badge && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {badge}
              </span>
            )}
          </div>
        </div>
        
        <div className="flex-shrink-0 ml-2">
          {isExpanded ? (
            <ChevronUp className="w-4 h-4 text-gray-500 transition-transform duration-200" />
          ) : (
            <ChevronDown className="w-4 h-4 text-gray-500 transition-transform duration-200" />
          )}
        </div>
      </button>

      {/* Content */}
      <div className={cn(
        'transition-all duration-300 ease-in-out overflow-hidden',
        isExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'
      )}>
        <div className={cn(
          'border-t border-gray-100',
          contentClassName
        )}>
          {children}
        </div>
      </div>
    </div>
  )
}

export default CollapsibleCard
