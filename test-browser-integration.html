<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器集成测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-section {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        .test-section:last-child {
            border-bottom: none;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
        }
        .test-urls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .url-card {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 15px;
            background: #f9fafb;
        }
        .url-card h4 {
            margin: 0 0 8px 0;
            color: #374151;
            font-size: 14px;
        }
        .url-card .url {
            font-family: monospace;
            font-size: 12px;
            color: #6b7280;
            word-break: break-all;
            margin-bottom: 10px;
        }
        .url-card .description {
            font-size: 13px;
            color: #4b5563;
            margin-bottom: 10px;
        }
        .test-btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
        }
        .test-btn:hover {
            background: #1d4ed8;
        }
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            display: none;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .status.loading {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .instructions {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h4 {
            margin: 0 0 10px 0;
            color: #0369a1;
        }
        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 5px;
            color: #0c4a6e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 浏览器集成功能测试</h1>
            <p>测试知识卡片应用的增强浏览器组件功能</p>
        </div>

        <div class="test-section">
            <div class="instructions">
                <h4>📋 测试说明</h4>
                <ol>
                    <li>确保知识卡片应用正在运行 (npm run dev)</li>
                    <li>点击下方的测试按钮，会在新窗口打开应用并自动输入测试URL</li>
                    <li>观察不同类型网站的加载效果和模式切换</li>
                    <li>测试浏览器导航功能（前进、后退、刷新等）</li>
                    <li>检查AI分析功能是否正常工作</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 iframe兼容性测试</h3>
            <p>这些网站应该能够直接在iframe中正常显示</p>
            <div class="test-urls">
                <div class="url-card">
                    <h4>Wikipedia</h4>
                    <div class="url">https://zh.wikipedia.org/wiki/人工智能</div>
                    <div class="description">维基百科页面，支持iframe嵌入</div>
                    <button class="test-btn" onclick="testUrl('https://zh.wikipedia.org/wiki/人工智能', this)">测试</button>
                    <div class="status"></div>
                </div>
                <div class="url-card">
                    <h4>MDN文档</h4>
                    <div class="url">https://developer.mozilla.org/zh-CN/docs/Web/JavaScript</div>
                    <div class="description">技术文档，iframe友好</div>
                    <button class="test-btn" onclick="testUrl('https://developer.mozilla.org/zh-CN/docs/Web/JavaScript', this)">测试</button>
                    <div class="status"></div>
                </div>
                <div class="url-card">
                    <h4>BBC新闻</h4>
                    <div class="url">https://www.bbc.com/news</div>
                    <div class="description">新闻网站，通常支持嵌入</div>
                    <button class="test-btn" onclick="testUrl('https://www.bbc.com/news', this)">测试</button>
                    <div class="status"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🚫 iframe限制测试</h3>
            <p>这些网站通常不允许iframe嵌入，应该自动切换到代理/渲染模式</p>
            <div class="test-urls">
                <div class="url-card">
                    <h4>GitHub</h4>
                    <div class="url">https://github.com/microsoft/vscode</div>
                    <div class="description">代码托管平台，禁止iframe</div>
                    <button class="test-btn" onclick="testUrl('https://github.com/microsoft/vscode', this)">测试</button>
                    <div class="status"></div>
                </div>
                <div class="url-card">
                    <h4>Stack Overflow</h4>
                    <div class="url">https://stackoverflow.com/questions/tagged/javascript</div>
                    <div class="description">技术问答网站，有iframe限制</div>
                    <button class="test-btn" onclick="testUrl('https://stackoverflow.com/questions/tagged/javascript', this)">测试</button>
                    <div class="status"></div>
                </div>
                <div class="url-card">
                    <h4>YouTube</h4>
                    <div class="url">https://www.youtube.com/watch?v=dQw4w9WgXcQ</div>
                    <div class="description">视频平台，严格禁止iframe</div>
                    <button class="test-btn" onclick="testUrl('https://www.youtube.com/watch?v=dQw4w9WgXcQ', this)">测试</button>
                    <div class="status"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>⚡ 动态内容测试</h3>
            <p>这些网站包含大量JavaScript和动态内容</p>
            <div class="test-urls">
                <div class="url-card">
                    <h4>React官网</h4>
                    <div class="url">https://react.dev/</div>
                    <div class="description">现代SPA应用</div>
                    <button class="test-btn" onclick="testUrl('https://react.dev/', this)">测试</button>
                    <div class="status"></div>
                </div>
                <div class="url-card">
                    <h4>Next.js文档</h4>
                    <div class="url">https://nextjs.org/docs</div>
                    <div class="description">技术文档，包含交互元素</div>
                    <button class="test-btn" onclick="testUrl('https://nextjs.org/docs', this)">测试</button>
                    <div class="status"></div>
                </div>
                <div class="url-card">
                    <h4>CodePen</h4>
                    <div class="url">https://codepen.io/trending</div>
                    <div class="description">在线代码编辑器</div>
                    <button class="test-btn" onclick="testUrl('https://codepen.io/trending', this)">测试</button>
                    <div class="status"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 API测试</h3>
            <p>直接测试代理API功能</p>
            <div class="test-urls">
                <div class="url-card">
                    <h4>兼容性检查API</h4>
                    <div class="url">/api/proxy (POST)</div>
                    <div class="description">检查URL的iframe兼容性</div>
                    <button class="test-btn" onclick="testCompatibilityAPI(this)">测试API</button>
                    <div class="status"></div>
                </div>
                <div class="url-card">
                    <h4>代理渲染API</h4>
                    <div class="url">/api/proxy?url=...&mode=render</div>
                    <div class="description">使用Playwright渲染页面</div>
                    <button class="test-btn" onclick="testProxyAPI(this)">测试API</button>
                    <div class="status"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testUrl(url, button) {
            const status = button.nextElementSibling;
            status.className = 'status loading';
            status.style.display = 'block';
            status.textContent = '正在打开应用...';
            
            // 打开知识卡片应用并传递URL
            const appUrl = `http://localhost:3000?url=${encodeURIComponent(url)}`;
            const newWindow = window.open(appUrl, '_blank');
            
            if (newWindow) {
                status.className = 'status success';
                status.textContent = '✅ 已在新窗口打开应用';
                
                // 尝试在新窗口中自动填入URL（如果同源）
                setTimeout(() => {
                    try {
                        if (newWindow.document) {
                            const input = newWindow.document.querySelector('input[type="text"], textarea');
                            if (input) {
                                input.value = url;
                                input.dispatchEvent(new Event('input', { bubbles: true }));
                            }
                        }
                    } catch (e) {
                        // 跨域限制，忽略
                    }
                }, 2000);
            } else {
                status.className = 'status error';
                status.textContent = '❌ 无法打开新窗口，请检查弹窗拦截设置';
            }
        }

        async function testCompatibilityAPI(button) {
            const status = button.nextElementSibling;
            status.className = 'status loading';
            status.style.display = 'block';
            status.textContent = '正在测试API...';
            
            try {
                const response = await fetch('/api/proxy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: 'https://github.com/microsoft/vscode',
                        mode: 'auto'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    status.className = 'status success';
                    status.textContent = `✅ API正常: ${data.message}`;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ API错误: ${error.message}`;
            }
        }

        async function testProxyAPI(button) {
            const status = button.nextElementSibling;
            status.className = 'status loading';
            status.style.display = 'block';
            status.textContent = '正在测试代理渲染...';
            
            try {
                const testUrl = 'https://example.com';
                const response = await fetch(`/api/proxy?url=${encodeURIComponent(testUrl)}&mode=proxy`);
                
                if (response.ok) {
                    const html = await response.text();
                    status.className = 'status success';
                    status.textContent = `✅ 代理成功，返回 ${html.length} 字符`;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ 代理错误: ${error.message}`;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 浏览器集成测试页面已加载');
            console.log('请确保知识卡片应用正在 http://localhost:3000 运行');
        });
    </script>
</body>
</html>
